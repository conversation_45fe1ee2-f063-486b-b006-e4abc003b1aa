'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Job, JobApplication, Bookmark } from '@/types';
import { NotificationBell } from '@/components/notifications';
import { LogoutButton } from '@/components/ui/LogoutButton';

export default function EmployeeDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [recentApplications, setRecentApplications] = useState<JobApplication[]>([]);
  const [bookmarkedJobs, setBookmarkedJobs] = useState<Job[]>([]);
  const [recommendedJobs, setRecommendedJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user.userType !== 'employee') {
      router.push('/employer/dashboard');
      return;
    }

    fetchDashboardData();
  }, [session, status, router]);

  const fetchDashboardData = async () => {
    try {
      // Fetch recent applications
      const applicationsRes = await fetch('/api/applications?limit=5');
      if (applicationsRes.ok) {
        const applicationsData = await applicationsRes.json();
        setRecentApplications(applicationsData.applications || []);
      }

      // Fetch bookmarked jobs
      const bookmarksRes = await fetch('/api/bookmarks?limit=5');
      if (bookmarksRes.ok) {
        const bookmarksData = await bookmarksRes.json();
        setBookmarkedJobs(bookmarksData.jobs || []);
      }

      // Fetch recommended jobs
      const jobsRes = await fetch('/api/jobs?limit=5&status=open');
      if (jobsRes.ok) {
        const jobsData = await jobsRes.json();
        setRecommendedJobs(jobsData.jobs || []);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Welcome back, {session?.user?.name}!</h1>
            <p className="text-gray-400">Here's what's happening with your job search</p>
          </div>
          <div className="flex items-center space-x-4">
            <NotificationBell />
            <Link href="/profile" className="btn-outline-primary">
              <i className="fas fa-user mr-2"></i>
              Profile
            </Link>
            <Link href="/jobs" className="btn-primary">
              <i className="fas fa-search mr-2"></i>
              Browse Jobs
            </Link>
            <LogoutButton />
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="p-3 bg-primary/20 rounded-full">
                  <i className="fas fa-file-alt text-primary text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-gray-400 text-sm">Total Applications</p>
                  <p className="text-2xl font-bold text-white">{recentApplications.length}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="p-3 bg-warning/20 rounded-full">
                  <i className="fas fa-bookmark text-warning text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-gray-400 text-sm">Bookmarked Jobs</p>
                  <p className="text-2xl font-bold text-white">{bookmarkedJobs.length}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="p-3 bg-success/20 rounded-full">
                  <i className="fas fa-briefcase text-success text-xl"></i>
                </div>
                <div className="ml-4">
                  <p className="text-gray-400 text-sm">Available Jobs</p>
                  <p className="text-2xl font-bold text-white">{recommendedJobs.length}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Applications */}
          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white">Recent Applications</h2>
                <Link href="/employee/applications" className="text-primary hover:text-primary-light">
                  View All
                </Link>
              </div>

              {recentApplications.length === 0 ? (
                <div className="text-center py-8">
                  <i className="fas fa-file-alt text-4xl text-gray-600 mb-4"></i>
                  <p className="text-gray-400">No applications yet</p>
                  <Link href="/jobs" className="btn-primary mt-4">
                    Start Applying
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentApplications.slice(0, 5).map((application) => (
                    <div key={application.id} className="flex items-center justify-between p-3 bg-dark-lighter rounded">
                      <div>
                        <h3 className="font-medium text-white">{application.job_details?.title}</h3>
                        <p className="text-sm text-gray-400">{application.job_details?.company}</p>
                        <p className="text-xs text-gray-500">Applied {new Date(application.applied_at).toLocaleDateString()}</p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        application.status === 'pending' ? 'bg-warning/20 text-warning' :
                        application.status === 'accepted' ? 'bg-success/20 text-success' :
                        'bg-danger/20 text-danger'
                      }`}>
                        {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Bookmarked Jobs */}
          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white">Bookmarked Jobs</h2>
                <Link href="/employee/bookmarks" className="text-primary hover:text-primary-light">
                  View All
                </Link>
              </div>

              {bookmarkedJobs.length === 0 ? (
                <div className="text-center py-8">
                  <i className="fas fa-bookmark text-4xl text-gray-600 mb-4"></i>
                  <p className="text-gray-400">No bookmarked jobs</p>
                  <Link href="/jobs" className="btn-primary mt-4">
                    Browse Jobs
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {bookmarkedJobs.slice(0, 5).map((job) => (
                    <div key={job.id} className="p-3 bg-dark-lighter rounded">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-medium text-white">{job.title}</h3>
                          <p className="text-sm text-gray-400">{job.company}</p>
                          <p className="text-sm text-gray-500">{job.location}</p>
                          {job.salary && (
                            <p className="text-sm text-primary">{job.salary}</p>
                          )}
                        </div>
                        <Link
                          href={`/jobs/${job.id}`}
                          className="btn-outline-primary text-xs"
                        >
                          View
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Recommended Jobs */}
        <div className="mt-8">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-white">Recommended Jobs</h2>
                <Link href="/jobs" className="text-primary hover:text-primary-light">
                  View All
                </Link>
              </div>

              {recommendedJobs.length === 0 ? (
                <div className="text-center py-8">
                  <i className="fas fa-briefcase text-4xl text-gray-600 mb-4"></i>
                  <p className="text-gray-400">No jobs available</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {recommendedJobs.slice(0, 6).map((job) => (
                    <div key={job.id} className="p-4 bg-dark-lighter rounded">
                      <h3 className="font-medium text-white mb-2">{job.title}</h3>
                      <p className="text-sm text-gray-400 mb-1">{job.company}</p>
                      <p className="text-sm text-gray-500 mb-2">{job.location}</p>
                      {job.salary && (
                        <p className="text-sm text-primary mb-3">{job.salary}</p>
                      )}
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {new Date(job.deadline).toLocaleDateString()}
                        </span>
                        <Link
                          href={`/jobs/${job.id}`}
                          className="btn-primary text-xs"
                        >
                          Apply
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
