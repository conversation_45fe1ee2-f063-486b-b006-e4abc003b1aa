/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/employer/jobs/new/page";
exports.ids = ["app/employer/jobs/new/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fjobs%2Fnew%2Fpage&page=%2Femployer%2Fjobs%2Fnew%2Fpage&appPaths=%2Femployer%2Fjobs%2Fnew%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fjobs%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fjobs%2Fnew%2Fpage&page=%2Femployer%2Fjobs%2Fnew%2Fpage&appPaths=%2Femployer%2Fjobs%2Fnew%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fjobs%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'employer',\n        {\n        children: [\n        'jobs',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employer/jobs/new/page.tsx */ \"(rsc)/./src/app/employer/jobs/new/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/employer/jobs/new/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/employer/jobs/new/page\",\n        pathname: \"/employer/jobs/new\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fjobs%2Fnew%2Fpage&page=%2Femployer%2Fjobs%2Fnew%2Fpage&appPaths=%2Femployer%2Fjobs%2Fnew%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fjobs%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3phY2hhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWlncmF0aW9uJTVDJTVDam9iemlwLW5leHRqcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6YWNoYSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q21pZ3JhdGlvbiU1QyU1Q2pvYnppcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6YWNoYSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q21pZ3JhdGlvbiU1QyU1Q2pvYnppcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQWlLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8/MWUxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHphY2hhXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcbWlncmF0aW9uXFxcXGpvYnppcC1uZXh0anNcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployer%5C%5Cjobs%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployer%5C%5Cjobs%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employer/jobs/new/page.tsx */ \"(ssr)/./src/app/employer/jobs/new/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3phY2hhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWlncmF0aW9uJTVDJTVDam9iemlwLW5leHRqcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2VtcGxveWVyJTVDJTVDam9icyU1QyU1Q25ldyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBMkkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qb2J6aXAtbmV4dGpzLz9mMWQwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcemFjaGFcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxtaWdyYXRpb25cXFxcam9iemlwLW5leHRqc1xcXFxzcmNcXFxcYXBwXFxcXGVtcGxveWVyXFxcXGpvYnNcXFxcbmV3XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployer%5C%5Cjobs%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/employer/jobs/new/page.tsx":
/*!********************************************!*\
  !*** ./src/app/employer/jobs/new/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewJobPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/FormError */ \"(ssr)/./src/components/ui/FormError.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction NewJobPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [savingDraft, setSavingDraft] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [generalError, setGeneralError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        title: \"\",\n        description: \"\",\n        location: \"\",\n        duration: \"\",\n        company: \"\",\n        salary: \"\",\n        employees_required: 1,\n        deadline: \"\",\n        status: \"open\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (status === \"loading\") return;\n        if (!session) {\n            router.push(\"/auth/login\");\n            return;\n        }\n        if (session.user.userType !== \"employer\") {\n            router.push(\"/employee/dashboard\");\n            return;\n        }\n    }, [\n        session,\n        status,\n        router\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) {\n            newErrors.title = \"Job title is required\";\n        } else if (formData.title.length < 3) {\n            newErrors.title = \"Job title must be at least 3 characters\";\n        }\n        if (!formData.description.trim()) {\n            newErrors.description = \"Job description is required\";\n        } else if (formData.description.length < 50) {\n            newErrors.description = \"Job description must be at least 50 characters\";\n        }\n        if (!formData.location.trim()) {\n            newErrors.location = \"Location is required\";\n        }\n        if (!formData.duration) {\n            newErrors.duration = \"Duration is required\";\n        }\n        if (!formData.company.trim()) {\n            newErrors.company = \"Company name is required\";\n        }\n        if (formData.employees_required < 1) {\n            newErrors.employees_required = \"At least 1 position is required\";\n        }\n        if (!formData.deadline) {\n            newErrors.deadline = \"Application deadline is required\";\n        } else {\n            const deadlineDate = new Date(formData.deadline);\n            const now = new Date();\n            if (deadlineDate <= now) {\n                newErrors.deadline = \"Deadline must be in the future\";\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setGeneralError(\"\");\n        setSuccessMessage(\"\");\n        if (!validateForm()) {\n            setGeneralError(\"Please fix the errors below before submitting\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/jobs\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    employees_required: parseInt(formData.employees_required.toString()),\n                    deadline: new Date(formData.deadline).toISOString(),\n                    status: \"open\"\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setSuccessMessage(\"Job posted successfully!\");\n                setTimeout(()=>{\n                    router.push(`/jobs/${data.job.id}`);\n                }, 1500);\n            } else {\n                if (data.details && Array.isArray(data.details)) {\n                    // Handle validation errors from API\n                    const apiErrors = {};\n                    data.details.forEach((error)=>{\n                        if (error.path && error.path.length > 0) {\n                            apiErrors[error.path[0]] = error.message;\n                        }\n                    });\n                    setErrors(apiErrors);\n                }\n                setGeneralError(data.error || \"Failed to create job posting\");\n            }\n        } catch (error) {\n            console.error(\"Error creating job:\", error);\n            setGeneralError(\"An error occurred while creating the job posting\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveDraft = async ()=>{\n        setGeneralError(\"\");\n        setSuccessMessage(\"\");\n        setSavingDraft(true);\n        try {\n            const response = await fetch(\"/api/jobs\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    employees_required: parseInt(formData.employees_required.toString()) || 1,\n                    deadline: formData.deadline ? new Date(formData.deadline).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),\n                    status: \"draft\"\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                setSuccessMessage(\"Draft saved successfully!\");\n                setTimeout(()=>{\n                    router.push(\"/employer/dashboard\");\n                }, 1500);\n            } else {\n                setGeneralError(data.error || \"Failed to save draft\");\n            }\n        } catch (error) {\n            console.error(\"Error saving draft:\", error);\n            setGeneralError(\"An error occurred while saving the draft\");\n        } finally{\n            setSavingDraft(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error for this field when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: \"\"\n                }));\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-dark-darker flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-spinner fa-spin text-4xl text-primary mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session || session.user.userType !== \"employer\") {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-darker\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-dark border-b border-gray-700 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Post a New Job\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Create a job posting to find the perfect candidates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/employer/dashboard\",\n                                className: \"btn-outline-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-arrow-left mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Back to Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: [\n                                    generalError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FormError, {\n                                        message: generalError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 32\n                                    }, this),\n                                    successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.SuccessMessage, {\n                                        message: successMessage,\n                                        onClose: ()=>setSuccessMessage(\"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 34\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Basic Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"title\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Job Title *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"title\",\n                                                                        name: \"title\",\n                                                                        value: formData.title,\n                                                                        onChange: handleChange,\n                                                                        required: true,\n                                                                        className: `form-input ${errors.title ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`,\n                                                                        placeholder: \"e.g. Senior Software Engineer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                        message: errors.title,\n                                                                        fieldName: \"title\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"company\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Company Name *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"company\",\n                                                                        name: \"company\",\n                                                                        value: formData.company,\n                                                                        onChange: handleChange,\n                                                                        required: true,\n                                                                        className: `form-input ${errors.company ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`,\n                                                                        placeholder: \"e.g. TechCorp Inc.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                        message: errors.company,\n                                                                        fieldName: \"company\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"location\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Location *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"location\",\n                                                                        name: \"location\",\n                                                                        value: formData.location,\n                                                                        onChange: handleChange,\n                                                                        required: true,\n                                                                        className: `form-input ${errors.location ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`,\n                                                                        placeholder: \"e.g. Nairobi, Kenya or Remote\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                        message: errors.location,\n                                                                        fieldName: \"location\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"duration\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Duration *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        id: \"duration\",\n                                                                        name: \"duration\",\n                                                                        value: formData.duration,\n                                                                        onChange: handleChange,\n                                                                        required: true,\n                                                                        className: `form-input ${errors.duration ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"\",\n                                                                                children: \"Select duration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 303,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Full-time\",\n                                                                                children: \"Full-time\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 304,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Part-time\",\n                                                                                children: \"Part-time\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 305,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Contract\",\n                                                                                children: \"Contract\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Temporary\",\n                                                                                children: \"Temporary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 307,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"Internship\",\n                                                                                children: \"Internship\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                                lineNumber: 308,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                        message: errors.duration,\n                                                                        fieldName: \"duration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"salary\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Salary (Optional)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        id: \"salary\",\n                                                                        name: \"salary\",\n                                                                        value: formData.salary,\n                                                                        onChange: handleChange,\n                                                                        className: `form-input ${errors.salary ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`,\n                                                                        placeholder: \"e.g. KSh 80,000 - KSh 120,000 per month\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                        message: errors.salary,\n                                                                        fieldName: \"salary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"employees_required\",\n                                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                        children: \"Number of Positions *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        id: \"employees_required\",\n                                                                        name: \"employees_required\",\n                                                                        value: formData.employees_required,\n                                                                        onChange: handleChange,\n                                                                        required: true,\n                                                                        min: \"1\",\n                                                                        className: `form-input ${errors.employees_required ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                        message: errors.employees_required,\n                                                                        fieldName: \"employees_required\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Job Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"description\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Description *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"description\",\n                                                                name: \"description\",\n                                                                value: formData.description,\n                                                                onChange: handleChange,\n                                                                required: true,\n                                                                rows: 10,\n                                                                className: `form-input ${errors.description ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`,\n                                                                placeholder: \"Provide a detailed description of the job role, responsibilities, requirements, and qualifications...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                message: errors.description,\n                                                                fieldName: \"description\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400 mt-2\",\n                                                                children: \"Include job responsibilities, required skills, qualifications, and any other relevant information. (Minimum 50 characters)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-white mb-4\",\n                                                        children: \"Application Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"deadline\",\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Application Deadline *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"datetime-local\",\n                                                                id: \"deadline\",\n                                                                name: \"deadline\",\n                                                                value: formData.deadline,\n                                                                onChange: handleChange,\n                                                                required: true,\n                                                                min: new Date().toISOString().slice(0, 16),\n                                                                className: `form-input ${errors.deadline ? \"border-red-500 focus:border-red-500 focus:ring-red-500\" : \"\"}`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_FormError__WEBPACK_IMPORTED_MODULE_5__.FieldError, {\n                                                                message: errors.deadline,\n                                                                fieldName: \"deadline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400 mt-2\",\n                                                                children: \"Set the last date and time when applications will be accepted.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between pt-6 border-t border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        href: \"/employer/dashboard\",\n                                                        className: \"btn-outline-secondary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-times mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Cancel\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: handleSaveDraft,\n                                                                disabled: savingDraft,\n                                                                className: \"btn-outline-primary\",\n                                                                children: savingDraft ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fas fa-spinner fa-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Saving Draft...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fas fa-save mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Save as Draft\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"submit\",\n                                                                disabled: loading,\n                                                                className: \"btn-primary\",\n                                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fas fa-spinner fa-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Creating...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fas fa-plus mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 435,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Post Job\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-lightbulb text-warning mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Tips for a Great Job Posting\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2 text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-check text-success mr-2 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Write a clear and descriptive job title\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-check text-success mr-2 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Include specific requirements and qualifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-check text-success mr-2 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Mention company culture and benefits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-check text-success mr-2 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Be transparent about salary and compensation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-check text-success mr-2 mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Set a reasonable application deadline\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\jobs\\\\new\\\\page.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/employer/jobs/new/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    cacheTime: 10 * 60 * 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVrRDtBQUNhO0FBQzlCO0FBRTFCLFNBQVNJLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBLENBQUMsSUFBTSxJQUFJRixvREFBV0EsQ0FBQztZQUNuRE0sZ0JBQWdCO2dCQUNkQyxTQUFTO29CQUNQQyxXQUFXLEtBQUs7b0JBQ2hCQyxXQUFXLEtBQUssS0FBSztnQkFDdkI7WUFDRjtRQUNGO0lBRUEscUJBQ0UsOERBQUNWLDREQUFlQTtrQkFDZCw0RUFBQ0UsNERBQW1CQTtZQUFDUyxRQUFRTDtzQkFDMUJEOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy50c3g/YmU4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3F1ZXJ5Q2xpZW50XSA9IHVzZVN0YXRlKCgpID0+IG5ldyBRdWVyeUNsaWVudCh7XG4gICAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICAgIHF1ZXJpZXM6IHtcbiAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgIGNhY2hlVGltZTogMTAgKiA2MCAqIDEwMDAsIC8vIDEwIG1pbnV0ZXNcbiAgICAgIH0sXG4gICAgfSxcbiAgfSkpO1xuXG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwidXNlU3RhdGUiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwiY2FjaGVUaW1lIiwiY2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/FormError.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/FormError.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FieldError: () => (/* binding */ FieldError),\n/* harmony export */   FormError: () => (/* binding */ FormError),\n/* harmony export */   SuccessMessage: () => (/* binding */ SuccessMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FormError,FieldError,SuccessMessage auto */ \n\nfunction FormError({ message, scrollToError = true }) {\n    const errorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (scrollToError && errorRef.current) {\n            errorRef.current.scrollIntoView({\n                behavior: \"smooth\",\n                block: \"center\"\n            });\n            errorRef.current.focus();\n        }\n    }, [\n        message,\n        scrollToError\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: errorRef,\n        className: \"flex items-center p-3 mb-4 text-sm text-red-400 bg-red-900/20 border border-red-500/30 rounded-lg\",\n        role: \"alert\",\n        tabIndex: -1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                className: \"fas fa-exclamation-triangle mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\nfunction FieldError({ message, fieldName }) {\n    const errorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (message && errorRef.current) {\n            // Scroll to the error field\n            const field = fieldName ? document.querySelector(`[name=\"${fieldName}\"]`) : null;\n            if (field) {\n                field.scrollIntoView({\n                    behavior: \"smooth\",\n                    block: \"center\"\n                });\n                field.focus();\n            }\n        }\n    }, [\n        message,\n        fieldName\n    ]);\n    if (!message) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: errorRef,\n        className: \"mt-1 text-sm text-red-400 flex items-center\",\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                className: \"fas fa-exclamation-circle mr-1 text-xs\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            message\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\nfunction SuccessMessage({ message, onClose }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (onClose) {\n            const timer = setTimeout(onClose, 5000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between p-3 mb-4 text-sm text-green-400 bg-green-900/20 border border-green-500/30 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-check-circle mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClose,\n                className: \"text-green-400 hover:text-green-300\",\n                \"aria-label\": \"Close message\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-times\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\FormError.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/FormError.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f0c4ceac8545\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZDcwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYwYzRjZWFjODU0NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/employer/jobs/new/page.tsx":
/*!********************************************!*\
  !*** ./src/app/employer/jobs/new/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\employer\jobs\new\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"JobZip - Find Your Next Career Opportunity\",\n    description: \"JobZip helps you find and apply to the best jobs in your field.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-dark-darker text-white min-h-screen`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-query","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fjobs%2Fnew%2Fpage&page=%2Femployer%2Fjobs%2Fnew%2Fpage&appPaths=%2Femployer%2Fjobs%2Fnew%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fjobs%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();