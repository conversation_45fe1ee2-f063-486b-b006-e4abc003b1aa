/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/employer/applications/page";
exports.ids = ["app/employer/applications/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fapplications%2Fpage&page=%2Femployer%2Fapplications%2Fpage&appPaths=%2Femployer%2Fapplications%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fapplications%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fapplications%2Fpage&page=%2Femployer%2Fapplications%2Fpage&appPaths=%2Femployer%2Fapplications%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fapplications%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'employer',\n        {\n        children: [\n        'applications',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employer/applications/page.tsx */ \"(rsc)/./src/app/employer/applications/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/employer/applications/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/employer/applications/page\",\n        pathname: \"/employer/applications\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fapplications%2Fpage&page=%2Femployer%2Fapplications%2Fpage&appPaths=%2Femployer%2Fapplications%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fapplications%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3phY2hhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWlncmF0aW9uJTVDJTVDam9iemlwLW5leHRqcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6YWNoYSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q21pZ3JhdGlvbiU1QyU1Q2pvYnppcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6YWNoYSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q21pZ3JhdGlvbiU1QyU1Q2pvYnppcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQWlLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8/MWUxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHphY2hhXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcbWlncmF0aW9uXFxcXGpvYnppcC1uZXh0anNcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployer%5C%5Capplications%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployer%5C%5Capplications%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employer/applications/page.tsx */ \"(ssr)/./src/app/employer/applications/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3phY2hhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWlncmF0aW9uJTVDJTVDam9iemlwLW5leHRqcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2VtcGxveWVyJTVDJTVDYXBwbGljYXRpb25zJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUE4SSIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYnppcC1uZXh0anMvPzkxOWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx6YWNoYVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXG1pZ3JhdGlvblxcXFxqb2J6aXAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZW1wbG95ZXJcXFxcYXBwbGljYXRpb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployer%5C%5Capplications%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/employer/applications/page.tsx":
/*!************************************************!*\
  !*** ./src/app/employer/applications/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmployerApplicationsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_LogoutButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LogoutButton */ \"(ssr)/./src/components/ui/LogoutButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction EmployerApplicationsPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [applications, setApplications] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (status === \"loading\") return;\n        if (!session) {\n            router.push(\"/auth/login\");\n            return;\n        }\n        if (session.user.userType !== \"employer\") {\n            router.push(\"/employee/dashboard\");\n            return;\n        }\n        fetchApplications();\n    }, [\n        session,\n        status,\n        router,\n        filter\n    ]);\n    const fetchApplications = async ()=>{\n        try {\n            const params = new URLSearchParams();\n            if (filter !== \"all\") {\n                params.append(\"status\", filter);\n            }\n            const response = await fetch(`/api/applications/received?${params.toString()}`);\n            if (response.ok) {\n                const data = await response.json();\n                setApplications(data.applications || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching applications:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleStatusChange = async (applicationId, newStatus)=>{\n        try {\n            const response = await fetch(`/api/applications/${applicationId}`, {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            if (response.ok) {\n                setApplications(applications.map((app)=>app.id === applicationId ? {\n                        ...app,\n                        status: newStatus\n                    } : app));\n                alert(`Application ${newStatus} successfully`);\n            } else {\n                alert(\"Failed to update application status\");\n            }\n        } catch (error) {\n            console.error(\"Error updating application:\", error);\n            alert(\"An error occurred while updating the application\");\n        }\n    };\n    const filteredApplications = applications.filter((app)=>app.applicant_details?.username?.toLowerCase().includes(searchTerm.toLowerCase()) || app.job_details?.title?.toLowerCase().includes(searchTerm.toLowerCase()) || app.job_details?.company?.toLowerCase().includes(searchTerm.toLowerCase()));\n    if (status === \"loading\" || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-dark-darker flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-spinner fa-spin text-4xl text-primary mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Loading applications...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-darker\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-dark border-b border-gray-700 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Job Applications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Review and manage candidate applications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/employer/dashboard\",\n                                    className: \"btn-outline-secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-arrow-left mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LogoutButton__WEBPACK_IMPORTED_MODULE_5__.LogoutButton, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex bg-dark rounded-lg p-1\",\n                                        children: [\n                                            \"all\",\n                                            \"pending\",\n                                            \"accepted\",\n                                            \"rejected\"\n                                        ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setFilter(status),\n                                                className: `px-4 py-2 rounded-md text-sm font-medium transition-colors ${filter === status ? \"bg-primary text-white\" : \"text-gray-400 hover:text-white\"}`,\n                                                children: status === \"all\" ? \"All Applications\" : status.charAt(0).toUpperCase() + status.slice(1)\n                                            }, status, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-400 text-sm\",\n                                        children: [\n                                            filteredApplications.length,\n                                            \" application\",\n                                            filteredApplications.length !== 1 ? \"s\" : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search applications...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"form-input pl-10 w-64\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: \"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    filteredApplications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"fas fa-file-alt text-6xl text-gray-600 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-2\",\n                                children: searchTerm ? \"No applications found\" : \"No applications yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: searchTerm ? \"Try adjusting your search terms\" : \"Applications will appear here when candidates apply to your jobs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: filteredApplications.map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                    className: \"fas fa-user text-primary text-lg\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-white\",\n                                                                        children: application.applicant_details?.first_name && application.applicant_details?.last_name ? `${application.applicant_details.first_name} ${application.applicant_details.last_name}` : application.applicant_details?.username\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 177,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: application.applicant_details?.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-sm font-medium text-gray-300 mb-1\",\n                                                                        children: \"Applied for\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white\",\n                                                                        children: application.job_details?.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: application.job_details?.company\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-sm font-medium text-gray-300 mb-1\",\n                                                                        children: \"Application Date\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white\",\n                                                                        children: new Date(application.applied_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 195,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: new Date(application.applied_at).toLocaleTimeString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    application.cover_letter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Cover Letter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-dark-lighter rounded-lg p-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm whitespace-pre-wrap line-clamp-3\",\n                                                                    children: application.cover_letter\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    application.applicant_details?.employee_profile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Skills & Experience\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                                children: [\n                                                                    application.applicant_details.employee_profile.skills && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"Skills:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 217,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-300\",\n                                                                                children: application.applicant_details.employee_profile.skills\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 218,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    application.applicant_details.employee_profile.experience && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"Experience:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 223,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-300\",\n                                                                                children: application.applicant_details.employee_profile.experience\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 224,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 flex flex-col items-end space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-3 py-1 rounded-full text-sm ${application.status === \"pending\" ? \"bg-warning/20 text-warning\" : application.status === \"accepted\" ? \"bg-success/20 text-success\" : \"bg-danger/20 text-danger\"}`,\n                                                        children: application.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                href: `/applications/${application.id}`,\n                                                                className: \"btn-outline-primary text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"fas fa-eye mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"View Details\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            application.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleStatusChange(application.id, \"accepted\"),\n                                                                        className: \"btn-success text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                                className: \"fas fa-check mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Accept\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleStatusChange(application.id, \"rejected\"),\n                                                                        className: \"btn-danger text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                                className: \"fas fa-times mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            \"Reject\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this)\n                            }, application.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employer\\\\applications\\\\page.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/employer/applications/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    cacheTime: 10 * 60 * 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVrRDtBQUNhO0FBQzlCO0FBRTFCLFNBQVNJLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBLENBQUMsSUFBTSxJQUFJRixvREFBV0EsQ0FBQztZQUNuRE0sZ0JBQWdCO2dCQUNkQyxTQUFTO29CQUNQQyxXQUFXLEtBQUs7b0JBQ2hCQyxXQUFXLEtBQUssS0FBSztnQkFDdkI7WUFDRjtRQUNGO0lBRUEscUJBQ0UsOERBQUNWLDREQUFlQTtrQkFDZCw0RUFBQ0UsNERBQW1CQTtZQUFDUyxRQUFRTDtzQkFDMUJEOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy50c3g/YmU4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3F1ZXJ5Q2xpZW50XSA9IHVzZVN0YXRlKCgpID0+IG5ldyBRdWVyeUNsaWVudCh7XG4gICAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICAgIHF1ZXJpZXM6IHtcbiAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgIGNhY2hlVGltZTogMTAgKiA2MCAqIDEwMDAsIC8vIDEwIG1pbnV0ZXNcbiAgICAgIH0sXG4gICAgfSxcbiAgfSkpO1xuXG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwidXNlU3RhdGUiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwiY2FjaGVUaW1lIiwiY2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LogoutButton.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/LogoutButton.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogoutButton: () => (/* binding */ LogoutButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ LogoutButton auto */ \n\n\nfunction LogoutButton({ className = \"\", showText = true, variant = \"button\" }) {\n    const [isLoggingOut, setIsLoggingOut] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleLogout = async ()=>{\n        setIsLoggingOut(true);\n        try {\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n                callbackUrl: \"/auth/login\",\n                redirect: true\n            });\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            setIsLoggingOut(false);\n        }\n    };\n    if (variant === \"link\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogout,\n            disabled: isLoggingOut,\n            className: `text-gray-400 hover:text-white transition-colors disabled:opacity-50 ${className}`,\n            children: isLoggingOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-spinner fa-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 13\n                    }, this),\n                    showText && \"Signing out...\"\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-sign-out-alt mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, this),\n                    showText && \"Sign Out\"\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleLogout,\n        disabled: isLoggingOut,\n        className: `btn-outline-secondary ${className}`,\n        children: isLoggingOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-spinner fa-spin mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this),\n                showText && \"Signing out...\"\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-sign-out-alt mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this),\n                showText && \"Sign Out\"\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9Mb2dvdXRCdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBDO0FBQ1Q7QUFRMUIsU0FBU0UsYUFBYSxFQUMzQkMsWUFBWSxFQUFFLEVBQ2RDLFdBQVcsSUFBSSxFQUNmQyxVQUFVLFFBQVEsRUFDQTtJQUNsQixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHTiwrQ0FBUUEsQ0FBQztJQUVqRCxNQUFNTyxlQUFlO1FBQ25CRCxnQkFBZ0I7UUFDaEIsSUFBSTtZQUNGLE1BQU1QLHdEQUFPQSxDQUFDO2dCQUNaUyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHNCQUFzQkE7WUFDcENKLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsSUFBSUYsWUFBWSxRQUFRO1FBQ3RCLHFCQUNFLDhEQUFDUTtZQUNDQyxTQUFTTjtZQUNUTyxVQUFVVDtZQUNWSCxXQUFXLENBQUMscUVBQXFFLEVBQUVBLFVBQVUsQ0FBQztzQkFFN0ZHLDZCQUNDOztrQ0FDRSw4REFBQ1U7d0JBQUViLFdBQVU7Ozs7OztvQkFDWkMsWUFBWTs7NkNBR2Y7O2tDQUNFLDhEQUFDWTt3QkFBRWIsV0FBVTs7Ozs7O29CQUNaQyxZQUFZOzs7Ozs7OztJQUt2QjtJQUVBLHFCQUNFLDhEQUFDUztRQUNDQyxTQUFTTjtRQUNUTyxVQUFVVDtRQUNWSCxXQUFXLENBQUMsc0JBQXNCLEVBQUVBLFVBQVUsQ0FBQztrQkFFOUNHLDZCQUNDOzs4QkFDRSw4REFBQ1U7b0JBQUViLFdBQVU7Ozs7OztnQkFDWkMsWUFBWTs7eUNBR2Y7OzhCQUNFLDhEQUFDWTtvQkFBRWIsV0FBVTs7Ozs7O2dCQUNaQyxZQUFZOzs7Ozs7OztBQUt2QiIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYnppcC1uZXh0anMvLi9zcmMvY29tcG9uZW50cy91aS9Mb2dvdXRCdXR0b24udHN4P2U2NmEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBzaWduT3V0IH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgTG9nb3V0QnV0dG9uUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIHNob3dUZXh0PzogYm9vbGVhbjtcbiAgdmFyaWFudD86ICdidXR0b24nIHwgJ2xpbmsnO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gTG9nb3V0QnV0dG9uKHsgXG4gIGNsYXNzTmFtZSA9ICcnLCBcbiAgc2hvd1RleHQgPSB0cnVlLCBcbiAgdmFyaWFudCA9ICdidXR0b24nIFxufTogTG9nb3V0QnV0dG9uUHJvcHMpIHtcbiAgY29uc3QgW2lzTG9nZ2luZ091dCwgc2V0SXNMb2dnaW5nT3V0XSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNMb2dnaW5nT3V0KHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBzaWduT3V0KHsgXG4gICAgICAgIGNhbGxiYWNrVXJsOiAnL2F1dGgvbG9naW4nLFxuICAgICAgICByZWRpcmVjdDogdHJ1ZSBcbiAgICAgIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzaWduaW5nIG91dDonLCBlcnJvcik7XG4gICAgICBzZXRJc0xvZ2dpbmdPdXQoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBpZiAodmFyaWFudCA9PT0gJ2xpbmsnKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxidXR0b25cbiAgICAgICAgb25DbGljaz17aGFuZGxlTG9nb3V0fVxuICAgICAgICBkaXNhYmxlZD17aXNMb2dnaW5nT3V0fVxuICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MCAke2NsYXNzTmFtZX1gfVxuICAgICAgPlxuICAgICAgICB7aXNMb2dnaW5nT3V0ID8gKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtc3Bpbm5lciBmYS1zcGluIG1yLTJcIj48L2k+XG4gICAgICAgICAgICB7c2hvd1RleHQgJiYgJ1NpZ25pbmcgb3V0Li4uJ31cbiAgICAgICAgICA8Lz5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAgPGkgY2xhc3NOYW1lPVwiZmFzIGZhLXNpZ24tb3V0LWFsdCBtci0yXCI+PC9pPlxuICAgICAgICAgICAge3Nob3dUZXh0ICYmICdTaWduIE91dCd9XG4gICAgICAgICAgPC8+XG4gICAgICAgICl9XG4gICAgICA8L2J1dHRvbj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8YnV0dG9uXG4gICAgICBvbkNsaWNrPXtoYW5kbGVMb2dvdXR9XG4gICAgICBkaXNhYmxlZD17aXNMb2dnaW5nT3V0fVxuICAgICAgY2xhc3NOYW1lPXtgYnRuLW91dGxpbmUtc2Vjb25kYXJ5ICR7Y2xhc3NOYW1lfWB9XG4gICAgPlxuICAgICAge2lzTG9nZ2luZ091dCA/IChcbiAgICAgICAgPD5cbiAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtc3Bpbm5lciBmYS1zcGluIG1yLTJcIj48L2k+XG4gICAgICAgICAge3Nob3dUZXh0ICYmICdTaWduaW5nIG91dC4uLid9XG4gICAgICAgIDwvPlxuICAgICAgKSA6IChcbiAgICAgICAgPD5cbiAgICAgICAgICA8aSBjbGFzc05hbWU9XCJmYXMgZmEtc2lnbi1vdXQtYWx0IG1yLTJcIj48L2k+XG4gICAgICAgICAge3Nob3dUZXh0ICYmICdTaWduIE91dCd9XG4gICAgICAgIDwvPlxuICAgICAgKX1cbiAgICA8L2J1dHRvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJzaWduT3V0IiwidXNlU3RhdGUiLCJMb2dvdXRCdXR0b24iLCJjbGFzc05hbWUiLCJzaG93VGV4dCIsInZhcmlhbnQiLCJpc0xvZ2dpbmdPdXQiLCJzZXRJc0xvZ2dpbmdPdXQiLCJoYW5kbGVMb2dvdXQiLCJjYWxsYmFja1VybCIsInJlZGlyZWN0IiwiZXJyb3IiLCJjb25zb2xlIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LogoutButton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f0c4ceac8545\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZDcwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYwYzRjZWFjODU0NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/employer/applications/page.tsx":
/*!************************************************!*\
  !*** ./src/app/employer/applications/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\employer\applications\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"JobZip - Find Your Next Career Opportunity\",\n    description: \"JobZip helps you find and apply to the best jobs in your field.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-dark-darker text-white min-h-screen`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-query","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployer%2Fapplications%2Fpage&page=%2Femployer%2Fapplications%2Fpage&appPaths=%2Femployer%2Fapplications%2Fpage&pagePath=private-next-app-dir%2Femployer%2Fapplications%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();