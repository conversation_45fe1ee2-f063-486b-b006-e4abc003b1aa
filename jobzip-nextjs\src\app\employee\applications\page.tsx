'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { JobApplication } from '@/types';
import { LogoutButton } from '@/components/ui/LogoutButton';

export default function EmployeeApplicationsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'accepted' | 'rejected'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user.userType !== 'employee') {
      router.push('/employer/dashboard');
      return;
    }

    fetchApplications();
  }, [session, status, router, filter]);

  const fetchApplications = async () => {
    try {
      const params = new URLSearchParams();
      if (filter !== 'all') {
        params.append('status', filter);
      }
      
      const response = await fetch(`/api/applications?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setApplications(data.applications || []);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleWithdrawApplication = async (applicationId: number) => {
    if (!confirm('Are you sure you want to withdraw this application?')) {
      return;
    }

    try {
      const response = await fetch(`/api/applications/${applicationId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setApplications(applications.filter(app => app.id !== applicationId));
        alert('Application withdrawn successfully');
      } else {
        alert('Failed to withdraw application');
      }
    } catch (error) {
      console.error('Error withdrawing application:', error);
      alert('An error occurred while withdrawing the application');
    }
  };

  const filteredApplications = applications.filter(app =>
    app.job_details?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.job_details?.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.job_details?.location?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
          <p className="text-gray-400">Loading your applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">My Applications</h1>
            <p className="text-gray-400">Track your job applications and their status</p>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/employee/dashboard" className="btn-outline-secondary">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Dashboard
            </Link>
            <Link href="/jobs" className="btn-primary">
              <i className="fas fa-search mr-2"></i>
              Browse Jobs
            </Link>
            <LogoutButton />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex bg-dark rounded-lg p-1">
              {(['all', 'pending', 'accepted', 'rejected'] as const).map((status) => (
                <button
                  key={status}
                  onClick={() => setFilter(status)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    filter === status
                      ? 'bg-primary text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {status === 'all' ? 'All Applications' : status.charAt(0).toUpperCase() + status.slice(1)}
                </button>
              ))}
            </div>
            <span className="text-gray-400 text-sm">
              {filteredApplications.length} application{filteredApplications.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder="Search applications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10 w-64"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        {/* Applications List */}
        {filteredApplications.length === 0 ? (
          <div className="text-center py-12">
            <i className="fas fa-file-alt text-6xl text-gray-600 mb-4"></i>
            <h3 className="text-xl font-semibold text-white mb-2">
              {searchTerm ? 'No applications found' : 'No applications yet'}
            </h3>
            <p className="text-gray-400 mb-6">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : 'Start applying to jobs to see your applications here'
              }
            </p>
            {!searchTerm && (
              <Link href="/jobs" className="btn-primary">
                <i className="fas fa-search mr-2"></i>
                Browse Jobs
              </Link>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredApplications.map((application) => (
              <div key={application.id} className="card">
                <div className="card-body">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">
                        {application.job_details?.title}
                      </h3>
                      <p className="text-gray-400 mb-1">{application.job_details?.company}</p>
                      <p className="text-gray-500 text-sm mb-2">
                        <i className="fas fa-map-marker-alt mr-1"></i>
                        {application.job_details?.location}
                      </p>
                      {application.job_details?.salary && (
                        <p className="text-primary text-sm font-medium mb-2">
                          {application.job_details.salary}
                        </p>
                      )}
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm ${
                      application.status === 'pending' 
                        ? 'bg-warning/20 text-warning'
                        : application.status === 'accepted'
                        ? 'bg-success/20 text-success'
                        : 'bg-danger/20 text-danger'
                    }`}>
                      {application.status}
                    </span>
                  </div>

                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-400 mb-2">
                      <span>
                        <i className="fas fa-calendar mr-1"></i>
                        Applied {new Date(application.applied_at).toLocaleDateString()}
                      </span>
                      <span>
                        <i className="fas fa-clock mr-1"></i>
                        {new Date(application.applied_at).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>

                  {application.cover_letter && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-300 mb-2">Your Cover Letter</h4>
                      <div className="bg-dark-lighter rounded-lg p-3">
                        <p className="text-gray-300 text-sm whitespace-pre-wrap line-clamp-3">
                          {application.cover_letter}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                    <div className="flex space-x-2">
                      <Link
                        href={`/jobs/${application.job_details?.id}`}
                        className="btn-outline-primary text-xs"
                      >
                        <i className="fas fa-eye mr-1"></i>
                        View Job
                      </Link>
                      <Link
                        href={`/applications/${application.id}`}
                        className="btn-outline-secondary text-xs"
                      >
                        <i className="fas fa-file-alt mr-1"></i>
                        View Application
                      </Link>
                    </div>
                    
                    {application.status === 'pending' && (
                      <button
                        onClick={() => handleWithdrawApplication(application.id)}
                        className="btn-danger text-xs"
                      >
                        <i className="fas fa-times mr-1"></i>
                        Withdraw
                      </button>
                    )}
                  </div>

                  {application.status === 'accepted' && (
                    <div className="mt-3 p-3 bg-success/10 border border-success/20 rounded-lg">
                      <div className="flex items-center">
                        <i className="fas fa-check-circle text-success mr-2"></i>
                        <span className="text-success text-sm font-medium">
                          Congratulations! Your application has been accepted.
                        </span>
                      </div>
                    </div>
                  )}

                  {application.status === 'rejected' && (
                    <div className="mt-3 p-3 bg-danger/10 border border-danger/20 rounded-lg">
                      <div className="flex items-center">
                        <i className="fas fa-times-circle text-danger mr-2"></i>
                        <span className="text-danger text-sm font-medium">
                          Unfortunately, your application was not selected.
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
