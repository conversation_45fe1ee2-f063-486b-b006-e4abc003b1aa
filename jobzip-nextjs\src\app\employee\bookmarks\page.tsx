'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Job } from '@/types';
import { LogoutButton } from '@/components/ui/LogoutButton';

export default function EmployeeBookmarksPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [bookmarkedJobs, setBookmarkedJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user.userType !== 'employee') {
      router.push('/employer/dashboard');
      return;
    }

    fetchBookmarks();
  }, [session, status, router]);

  const fetchBookmarks = async () => {
    try {
      const response = await fetch('/api/bookmarks');
      if (response.ok) {
        const data = await response.json();
        setBookmarkedJobs(data.jobs || []);
      }
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveBookmark = async (jobId: number) => {
    try {
      const response = await fetch(`/api/bookmarks/toggle/${jobId}`, {
        method: 'POST',
      });

      if (response.ok) {
        setBookmarkedJobs(bookmarkedJobs.filter(job => job.id !== jobId));
      } else {
        alert('Failed to remove bookmark');
      }
    } catch (error) {
      console.error('Error removing bookmark:', error);
      alert('An error occurred while removing the bookmark');
    }
  };

  const filteredJobs = bookmarkedJobs.filter(job =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
          <p className="text-gray-400">Loading your bookmarks...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Bookmarked Jobs</h1>
            <p className="text-gray-400">Your saved job opportunities</p>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/employee/dashboard" className="btn-outline-secondary">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Dashboard
            </Link>
            <Link href="/jobs" className="btn-primary">
              <i className="fas fa-search mr-2"></i>
              Browse Jobs
            </Link>
            <LogoutButton />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Search */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <span className="text-gray-400 text-sm">
              {filteredJobs.length} bookmarked job{filteredJobs.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder="Search bookmarked jobs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10 w-64"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        {/* Jobs Grid */}
        {filteredJobs.length === 0 ? (
          <div className="text-center py-12">
            <i className="fas fa-bookmark text-6xl text-gray-600 mb-4"></i>
            <h3 className="text-xl font-semibold text-white mb-2">
              {searchTerm ? 'No bookmarked jobs found' : 'No bookmarked jobs yet'}
            </h3>
            <p className="text-gray-400 mb-6">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : 'Start bookmarking jobs you\'re interested in to see them here'
              }
            </p>
            {!searchTerm && (
              <Link href="/jobs" className="btn-primary">
                <i className="fas fa-search mr-2"></i>
                Browse Jobs
              </Link>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredJobs.map((job) => (
              <div key={job.id} className="card">
                <div className="card-body">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">{job.title}</h3>
                      <p className="text-gray-400 text-sm mb-1">{job.company}</p>
                      <p className="text-gray-500 text-sm mb-2">
                        <i className="fas fa-map-marker-alt mr-1"></i>
                        {job.location}
                      </p>
                      <p className="text-gray-500 text-sm mb-2">
                        <i className="fas fa-clock mr-1"></i>
                        {job.duration}
                      </p>
                      {job.salary && (
                        <p className="text-primary text-sm font-medium mb-2">{job.salary}</p>
                      )}
                    </div>
                    <button
                      onClick={() => handleRemoveBookmark(job.id)}
                      className="text-warning hover:text-warning-light transition-colors"
                      title="Remove bookmark"
                    >
                      <i className="fas fa-bookmark text-lg"></i>
                    </button>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>
                      <i className="fas fa-users mr-1"></i>
                      {job.employees_required} position{job.employees_required > 1 ? 's' : ''}
                    </span>
                    <span>
                      <i className="fas fa-calendar mr-1"></i>
                      Deadline: {new Date(job.deadline).toLocaleDateString()}
                    </span>
                  </div>

                  <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                    {job.description}
                  </p>

                  <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                    <div className="flex space-x-2">
                      <Link
                        href={`/jobs/${job.id}`}
                        className="btn-outline-primary text-xs"
                      >
                        <i className="fas fa-eye mr-1"></i>
                        View Details
                      </Link>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {job.has_applied ? (
                        <span className="text-success text-xs">
                          <i className="fas fa-check mr-1"></i>
                          Applied
                        </span>
                      ) : (
                        <Link
                          href={`/jobs/${job.id}`}
                          className="btn-primary text-xs"
                        >
                          <i className="fas fa-paper-plane mr-1"></i>
                          Apply Now
                        </Link>
                      )}
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-700 text-xs text-gray-500">
                    <div className="flex items-center justify-between">
                      <span>Posted {new Date(job.created_at).toLocaleDateString()}</span>
                      <span className={`px-2 py-1 rounded-full ${
                        job.status === 'open' && new Date(job.deadline) > new Date()
                          ? 'bg-success/20 text-success'
                          : 'bg-danger/20 text-danger'
                      }`}>
                        {job.status === 'open' && new Date(job.deadline) > new Date() ? 'Open' : 'Closed'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
