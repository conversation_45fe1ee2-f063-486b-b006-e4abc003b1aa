'use client';

import { useEffect, useRef } from 'react';

interface FormErrorProps {
  message: string;
  scrollToError?: boolean;
}

export function FormError({ message, scrollToError = true }: FormErrorProps) {
  const errorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollToError && errorRef.current) {
      errorRef.current.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
      errorRef.current.focus();
    }
  }, [message, scrollToError]);

  return (
    <div 
      ref={errorRef}
      className="flex items-center p-3 mb-4 text-sm text-red-400 bg-red-900/20 border border-red-500/30 rounded-lg"
      role="alert"
      tabIndex={-1}
    >
      <i className="fas fa-exclamation-triangle mr-2"></i>
      <span>{message}</span>
    </div>
  );
}

interface FieldErrorProps {
  message?: string;
  fieldName?: string;
}

export function FieldError({ message, fieldName }: FieldErrorProps) {
  const errorRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    if (message && errorRef.current) {
      // Scroll to the error field
      const field = fieldName ? document.querySelector(`[name="${fieldName}"]`) : null;
      if (field) {
        field.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center' 
        });
        (field as HTMLElement).focus();
      }
    }
  }, [message, fieldName]);

  if (!message) return null;

  return (
    <p 
      ref={errorRef}
      className="mt-1 text-sm text-red-400 flex items-center"
      role="alert"
    >
      <i className="fas fa-exclamation-circle mr-1 text-xs"></i>
      {message}
    </p>
  );
}

interface SuccessMessageProps {
  message: string;
  onClose?: () => void;
}

export function SuccessMessage({ message, onClose }: SuccessMessageProps) {
  useEffect(() => {
    if (onClose) {
      const timer = setTimeout(onClose, 5000);
      return () => clearTimeout(timer);
    }
  }, [onClose]);

  return (
    <div className="flex items-center justify-between p-3 mb-4 text-sm text-green-400 bg-green-900/20 border border-green-500/30 rounded-lg">
      <div className="flex items-center">
        <i className="fas fa-check-circle mr-2"></i>
        <span>{message}</span>
      </div>
      {onClose && (
        <button
          onClick={onClose}
          className="text-green-400 hover:text-green-300"
          aria-label="Close message"
        >
          <i className="fas fa-times"></i>
        </button>
      )}
    </div>
  );
}
