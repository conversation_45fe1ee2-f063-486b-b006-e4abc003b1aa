'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { JobApplication } from '@/types';
import { LogoutButton } from '@/components/ui/LogoutButton';

export default function EmployerApplicationsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'accepted' | 'rejected'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user.userType !== 'employer') {
      router.push('/employee/dashboard');
      return;
    }

    fetchApplications();
  }, [session, status, router, filter]);

  const fetchApplications = async () => {
    try {
      const params = new URLSearchParams();
      if (filter !== 'all') {
        params.append('status', filter);
      }
      
      const response = await fetch(`/api/applications/received?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setApplications(data.applications || []);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (applicationId: number, newStatus: 'accepted' | 'rejected') => {
    try {
      const response = await fetch(`/api/applications/${applicationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setApplications(applications.map(app => 
          app.id === applicationId 
            ? { ...app, status: newStatus }
            : app
        ));
        alert(`Application ${newStatus} successfully`);
      } else {
        alert('Failed to update application status');
      }
    } catch (error) {
      console.error('Error updating application:', error);
      alert('An error occurred while updating the application');
    }
  };

  const filteredApplications = applications.filter(app =>
    (app.applicant_details?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
     app.job_details?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
     app.job_details?.company?.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
          <p className="text-gray-400">Loading applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Job Applications</h1>
            <p className="text-gray-400">Review and manage candidate applications</p>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/employer/dashboard" className="btn-outline-secondary">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Dashboard
            </Link>
            <LogoutButton />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex bg-dark rounded-lg p-1">
              {(['all', 'pending', 'accepted', 'rejected'] as const).map((status) => (
                <button
                  key={status}
                  onClick={() => setFilter(status)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    filter === status
                      ? 'bg-primary text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {status === 'all' ? 'All Applications' : status.charAt(0).toUpperCase() + status.slice(1)}
                </button>
              ))}
            </div>
            <span className="text-gray-400 text-sm">
              {filteredApplications.length} application{filteredApplications.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder="Search applications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10 w-64"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        {/* Applications List */}
        {filteredApplications.length === 0 ? (
          <div className="text-center py-12">
            <i className="fas fa-file-alt text-6xl text-gray-600 mb-4"></i>
            <h3 className="text-xl font-semibold text-white mb-2">
              {searchTerm ? 'No applications found' : 'No applications yet'}
            </h3>
            <p className="text-gray-400 mb-6">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : 'Applications will appear here when candidates apply to your jobs'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredApplications.map((application) => (
              <div key={application.id} className="card">
                <div className="card-body">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                          <i className="fas fa-user text-primary text-lg"></i>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">
                            {application.applicant_details?.first_name && application.applicant_details?.last_name
                              ? `${application.applicant_details.first_name} ${application.applicant_details.last_name}`
                              : application.applicant_details?.username
                            }
                          </h3>
                          <p className="text-gray-400 text-sm">{application.applicant_details?.email}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-300 mb-1">Applied for</h4>
                          <p className="text-white">{application.job_details?.title}</p>
                          <p className="text-gray-400 text-sm">{application.job_details?.company}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-300 mb-1">Application Date</h4>
                          <p className="text-white">{new Date(application.applied_at).toLocaleDateString()}</p>
                          <p className="text-gray-400 text-sm">{new Date(application.applied_at).toLocaleTimeString()}</p>
                        </div>
                      </div>

                      {application.cover_letter && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-300 mb-2">Cover Letter</h4>
                          <div className="bg-dark-lighter rounded-lg p-3">
                            <p className="text-gray-300 text-sm whitespace-pre-wrap line-clamp-3">
                              {application.cover_letter}
                            </p>
                          </div>
                        </div>
                      )}

                      {application.applicant_details?.employee_profile && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-300 mb-2">Skills & Experience</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            {application.applicant_details.employee_profile.skills && (
                              <div>
                                <span className="text-gray-400">Skills:</span>
                                <p className="text-gray-300">{application.applicant_details.employee_profile.skills}</p>
                              </div>
                            )}
                            {application.applicant_details.employee_profile.experience && (
                              <div>
                                <span className="text-gray-400">Experience:</span>
                                <p className="text-gray-300">{application.applicant_details.employee_profile.experience}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="ml-6 flex flex-col items-end space-y-3">
                      <span className={`px-3 py-1 rounded-full text-sm ${
                        application.status === 'pending' 
                          ? 'bg-warning/20 text-warning'
                          : application.status === 'accepted'
                          ? 'bg-success/20 text-success'
                          : 'bg-danger/20 text-danger'
                      }`}>
                        {application.status}
                      </span>

                      <div className="flex flex-col space-y-2">
                        <Link
                          href={`/applications/${application.id}`}
                          className="btn-outline-primary text-xs"
                        >
                          <i className="fas fa-eye mr-1"></i>
                          View Details
                        </Link>
                        
                        {application.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleStatusChange(application.id, 'accepted')}
                              className="btn-success text-xs"
                            >
                              <i className="fas fa-check mr-1"></i>
                              Accept
                            </button>
                            <button
                              onClick={() => handleStatusChange(application.id, 'rejected')}
                              className="btn-danger text-xs"
                            >
                              <i className="fas fa-times mr-1"></i>
                              Reject
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
