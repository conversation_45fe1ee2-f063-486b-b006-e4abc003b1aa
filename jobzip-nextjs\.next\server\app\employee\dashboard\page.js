/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/employee/dashboard/page";
exports.ids = ["app/employee/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'employee',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employee/dashboard/page.tsx */ \"(rsc)/./src/app/employee/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/employee/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/employee/dashboard/page\",\n        pathname: \"/employee/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3phY2hhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWlncmF0aW9uJTVDJTVDam9iemlwLW5leHRqcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6YWNoYSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q21pZ3JhdGlvbiU1QyU1Q2pvYnppcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6YWNoYSU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q21pZ3JhdGlvbiU1QyU1Q2pvYnppcC1uZXh0anMlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlByb3ZpZGVycyUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQWlLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8/MWUxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHphY2hhXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcbWlncmF0aW9uXFxcXGpvYnppcC1uZXh0anNcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/employee/dashboard/page.tsx */ \"(ssr)/./src/app/employee/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3phY2hhJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDbWlncmF0aW9uJTVDJTVDam9iemlwLW5leHRqcyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2VtcGxveWVlJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUEySSIsInNvdXJjZXMiOlsid2VicGFjazovL2pvYnppcC1uZXh0anMvP2NjYzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx6YWNoYVxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXG1pZ3JhdGlvblxcXFxqb2J6aXAtbmV4dGpzXFxcXHNyY1xcXFxhcHBcXFxcZW1wbG95ZWVcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czacha%5C%5COneDrive%5C%5CDesktop%5C%5Cmigration%5C%5Cjobzip-nextjs%5C%5Csrc%5C%5Capp%5C%5Cemployee%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/employee/dashboard/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/employee/dashboard/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmployeeDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_notifications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notifications */ \"(ssr)/./src/components/notifications.tsx\");\n/* harmony import */ var _components_ui_LogoutButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/LogoutButton */ \"(ssr)/./src/components/ui/LogoutButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction EmployeeDashboard() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [recentApplications, setRecentApplications] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [bookmarkedJobs, setBookmarkedJobs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [recommendedJobs, setRecommendedJobs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (status === \"loading\") return;\n        if (!session) {\n            router.push(\"/auth/login\");\n            return;\n        }\n        if (session.user.userType !== \"employee\") {\n            router.push(\"/employer/dashboard\");\n            return;\n        }\n        fetchDashboardData();\n    }, [\n        session,\n        status,\n        router\n    ]);\n    const fetchDashboardData = async ()=>{\n        try {\n            // Fetch recent applications\n            const applicationsRes = await fetch(\"/api/applications?limit=5\");\n            if (applicationsRes.ok) {\n                const applicationsData = await applicationsRes.json();\n                setRecentApplications(applicationsData.applications || []);\n            }\n            // Fetch bookmarked jobs\n            const bookmarksRes = await fetch(\"/api/bookmarks?limit=5\");\n            if (bookmarksRes.ok) {\n                const bookmarksData = await bookmarksRes.json();\n                setBookmarkedJobs(bookmarksData.jobs || []);\n            }\n            // Fetch recommended jobs\n            const jobsRes = await fetch(\"/api/jobs?limit=5&status=open\");\n            if (jobsRes.ok) {\n                const jobsData = await jobsRes.json();\n                setRecommendedJobs(jobsData.jobs || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching dashboard data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (status === \"loading\" || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-400\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-dark-darker\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-dark border-b border-gray-700 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        session?.user?.name,\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Here's what's happening with your job search\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications__WEBPACK_IMPORTED_MODULE_5__.NotificationBell, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/profile\",\n                                    className: \"btn-outline-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-user mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Profile\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/jobs\",\n                                    className: \"btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-search mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Browse Jobs\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LogoutButton__WEBPACK_IMPORTED_MODULE_6__.LogoutButton, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-primary/20 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-file-alt text-primary text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Total Applications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: recentApplications.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-warning/20 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-bookmark text-warning text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Bookmarked Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: bookmarkedJobs.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-success/20 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-briefcase text-success text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Available Jobs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: recommendedJobs.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-white\",\n                                                    children: \"Recent Applications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/employee/applications\",\n                                                    className: \"text-primary hover:text-primary-light\",\n                                                    children: \"View All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        recentApplications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-file-alt text-4xl text-gray-600 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"No applications yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/jobs\",\n                                                    className: \"btn-primary mt-4\",\n                                                    children: \"Start Applying\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: recentApplications.slice(0, 5).map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-dark-lighter rounded\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: application.job_details?.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: application.job_details?.company\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Applied \",\n                                                                        new Date(application.applied_at).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `px-2 py-1 rounded text-xs font-medium ${application.status === \"pending\" ? \"bg-warning/20 text-warning\" : application.status === \"accepted\" ? \"bg-success/20 text-success\" : \"bg-danger/20 text-danger\"}`,\n                                                            children: application.status.charAt(0).toUpperCase() + application.status.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, application.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-body\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-white\",\n                                                    children: \"Bookmarked Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/employee/bookmarks\",\n                                                    className: \"text-primary hover:text-primary-light\",\n                                                    children: \"View All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        bookmarkedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"fas fa-bookmark text-4xl text-gray-600 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400\",\n                                                    children: \"No bookmarked jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/jobs\",\n                                                    className: \"btn-primary mt-4\",\n                                                    children: \"Browse Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: bookmarkedJobs.slice(0, 5).map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-dark-lighter rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-white\",\n                                                                        children: job.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: job.company\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: job.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 213,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    job.salary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-primary\",\n                                                                        children: job.salary\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                href: `/jobs/${job.id}`,\n                                                                className: \"btn-outline-primary text-xs\",\n                                                                children: \"View\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, job.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"Recommended Jobs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/jobs\",\n                                                className: \"text-primary hover:text-primary-light\",\n                                                children: \"View All\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    recommendedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: \"fas fa-briefcase text-4xl text-gray-600 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400\",\n                                                children: \"No jobs available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: recommendedJobs.slice(0, 6).map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-dark-lighter rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-white mb-2\",\n                                                        children: job.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: job.company\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mb-2\",\n                                                        children: job.location\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    job.salary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-primary mb-3\",\n                                                        children: job.salary\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(job.deadline).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                href: `/jobs/${job.id}`,\n                                                                className: \"btn-primary text-xs\",\n                                                                children: \"Apply\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, job.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\employee\\\\dashboard\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/employee/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/notifications.tsx":
/*!******************************************!*\
  !*** ./src/components/notifications.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBell: () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ NotificationBell auto */ \n\n\nfunction NotificationBell({ className = \"\" }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [showDropdown, setShowDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (session) {\n            fetchNotifications();\n        }\n    }, [\n        session\n    ]);\n    const fetchNotifications = async ()=>{\n        try {\n            const response = await fetch(\"/api/user/notifications?limit=10\");\n            if (response.ok) {\n                const data = await response.json();\n                setNotifications(data.notifications || []);\n                setUnreadCount(data.unread_count || 0);\n            }\n        } catch (error) {\n            console.error(\"Error fetching notifications:\", error);\n        }\n    };\n    const markAsRead = async (notificationIds)=>{\n        try {\n            const response = await fetch(\"/api/user/notifications\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notification_ids: notificationIds\n                })\n            });\n            if (response.ok) {\n                fetchNotifications(); // Refresh notifications\n            }\n        } catch (error) {\n            console.error(\"Error marking notifications as read:\", error);\n        }\n    };\n    const markAllAsRead = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/user/notifications\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    mark_all_read: true\n                })\n            });\n            if (response.ok) {\n                fetchNotifications(); // Refresh notifications\n            }\n        } catch (error) {\n            console.error(\"Error marking all notifications as read:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNotificationClick = (notification)=>{\n        if (!notification.is_read) {\n            markAsRead([\n                notification.id\n            ]);\n        }\n        setShowDropdown(false);\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"application\":\n                return \"fas fa-file-alt\";\n            case \"listing\":\n                return \"fas fa-briefcase\";\n            case \"message\":\n                return \"fas fa-envelope\";\n            default:\n                return \"fas fa-bell\";\n        }\n    };\n    const formatTimeAgo = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n        if (diffInSeconds < 60) return \"Just now\";\n        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n        return date.toLocaleDateString();\n    };\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowDropdown(!showDropdown),\n                className: \"relative p-2 text-gray-400 hover:text-white transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-bell text-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 bg-danger text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\",\n                        children: unreadCount > 9 ? \"9+\" : unreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            showDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-10\",\n                        onClick: ()=>setShowDropdown(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute right-0 mt-2 w-80 bg-dark border border-gray-700 rounded-lg shadow-lg z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: markAllAsRead,\n                                            disabled: loading,\n                                            className: \"text-sm text-primary hover:text-primary-light\",\n                                            children: loading ? \"Marking...\" : \"Mark all read\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-h-96 overflow-y-auto\",\n                                children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 text-center text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-bell-slash text-2xl mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y divide-gray-700\",\n                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>handleNotificationClick(notification),\n                                            className: `p-4 cursor-pointer hover:bg-dark-lighter transition-colors ${!notification.is_read ? \"bg-primary/5\" : \"\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `p-2 rounded-full ${!notification.is_read ? \"bg-primary/20\" : \"bg-gray-700\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: `${getNotificationIcon(notification.notification_type)} ${!notification.is_read ? \"text-primary\" : \"text-gray-400\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: `text-sm ${!notification.is_read ? \"text-white font-medium\" : \"text-gray-300\"}`,\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mt-1\",\n                                                                children: formatTimeAgo(notification.created_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !notification.is_read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-primary rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, notification.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-t border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowDropdown(false);\n                                    // Navigate to full notifications page if implemented\n                                    },\n                                    className: \"w-full text-center text-sm text-primary hover:text-primary-light\",\n                                    children: \"View all notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\notifications.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/notifications.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 60 * 1000,\n                    cacheTime: 10 * 60 * 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider, {\n            client: queryClient,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVrRDtBQUNhO0FBQzlCO0FBRTFCLFNBQVNJLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBLENBQUMsSUFBTSxJQUFJRixvREFBV0EsQ0FBQztZQUNuRE0sZ0JBQWdCO2dCQUNkQyxTQUFTO29CQUNQQyxXQUFXLEtBQUs7b0JBQ2hCQyxXQUFXLEtBQUssS0FBSztnQkFDdkI7WUFDRjtRQUNGO0lBRUEscUJBQ0UsOERBQUNWLDREQUFlQTtrQkFDZCw0RUFBQ0UsNERBQW1CQTtZQUFDUyxRQUFRTDtzQkFDMUJEOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy50c3g/YmU4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3F1ZXJ5Q2xpZW50XSA9IHVzZVN0YXRlKCgpID0+IG5ldyBRdWVyeUNsaWVudCh7XG4gICAgZGVmYXVsdE9wdGlvbnM6IHtcbiAgICAgIHF1ZXJpZXM6IHtcbiAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgIGNhY2hlVGltZTogMTAgKiA2MCAqIDEwMDAsIC8vIDEwIG1pbnV0ZXNcbiAgICAgIH0sXG4gICAgfSxcbiAgfSkpO1xuXG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwidXNlU3RhdGUiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwiY2FjaGVUaW1lIiwiY2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LogoutButton.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/LogoutButton.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogoutButton: () => (/* binding */ LogoutButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ LogoutButton auto */ \n\n\nfunction LogoutButton({ className = \"\", showText = true, variant = \"button\" }) {\n    const [isLoggingOut, setIsLoggingOut] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleLogout = async ()=>{\n        setIsLoggingOut(true);\n        try {\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n                callbackUrl: \"/auth/login\",\n                redirect: true\n            });\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            setIsLoggingOut(false);\n        }\n    };\n    if (variant === \"link\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogout,\n            disabled: isLoggingOut,\n            className: `text-gray-400 hover:text-white transition-colors disabled:opacity-50 ${className}`,\n            children: isLoggingOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-spinner fa-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 13\n                    }, this),\n                    showText && \"Signing out...\"\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-sign-out-alt mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, this),\n                    showText && \"Sign Out\"\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleLogout,\n        disabled: isLoggingOut,\n        className: `btn-outline-secondary ${className}`,\n        children: isLoggingOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-spinner fa-spin mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this),\n                showText && \"Signing out...\"\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"fas fa-sign-out-alt mr-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this),\n                showText && \"Sign Out\"\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\components\\\\ui\\\\LogoutButton.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LogoutButton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f0c4ceac8545\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vam9iemlwLW5leHRqcy8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZDcwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImYwYzRjZWFjODU0NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/employee/dashboard/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/employee/dashboard/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\app\employee\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"JobZip - Find Your Next Career Opportunity\",\n    description: \"JobZip helps you find and apply to the best jobs in your field.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-dark-darker text-white min-h-screen`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\migration\\\\jobzip-nextjs\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Desktop\migration\jobzip-nextjs\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-query","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Femployee%2Fdashboard%2Fpage&page=%2Femployee%2Fdashboard%2Fpage&appPaths=%2Femployee%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Femployee%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czacha%5COneDrive%5CDesktop%5Cmigration%5Cjobzip-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();