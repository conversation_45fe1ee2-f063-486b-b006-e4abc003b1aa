'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Job } from '@/types';
import { LogoutButton } from '@/components/ui/LogoutButton';

export default function EmployerJobsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'open' | 'closed' | 'draft'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/login');
      return;
    }

    if (session.user.userType !== 'employer') {
      router.push('/employee/dashboard');
      return;
    }

    fetchJobs();
  }, [session, status, router, filter]);

  const fetchJobs = async () => {
    try {
      const params = new URLSearchParams();
      if (filter !== 'all') {
        params.append('status', filter);
      }
      
      const response = await fetch(`/api/jobs/my-jobs?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setJobs(data.jobs || []);
      }
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteJob = async (jobId: number) => {
    if (!confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/jobs/${jobId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setJobs(jobs.filter(job => job.id !== jobId));
        alert('Job deleted successfully');
      } else {
        alert('Failed to delete job');
      }
    } catch (error) {
      console.error('Error deleting job:', error);
      alert('An error occurred while deleting the job');
    }
  };

  const filteredJobs = jobs.filter(job =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-dark-darker flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary mb-4"></i>
          <p className="text-gray-400">Loading your jobs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dark-darker">
      {/* Header */}
      <header className="bg-dark border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">My Job Postings</h1>
            <p className="text-gray-400">Manage all your job listings</p>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/employer/dashboard" className="btn-outline-secondary">
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Dashboard
            </Link>
            <Link href="/employer/jobs/new" className="btn-primary">
              <i className="fas fa-plus mr-2"></i>
              Post New Job
            </Link>
            <LogoutButton />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex bg-dark rounded-lg p-1">
              {(['all', 'open', 'closed', 'draft'] as const).map((status) => (
                <button
                  key={status}
                  onClick={() => setFilter(status)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    filter === status
                      ? 'bg-primary text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {status === 'all' ? 'All Jobs' : status.charAt(0).toUpperCase() + status.slice(1)}
                </button>
              ))}
            </div>
            <span className="text-gray-400 text-sm">
              {filteredJobs.length} job{filteredJobs.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder="Search jobs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10 w-64"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>

        {/* Jobs Grid */}
        {filteredJobs.length === 0 ? (
          <div className="text-center py-12">
            <i className="fas fa-briefcase text-6xl text-gray-600 mb-4"></i>
            <h3 className="text-xl font-semibold text-white mb-2">
              {searchTerm ? 'No jobs found' : 'No jobs posted yet'}
            </h3>
            <p className="text-gray-400 mb-6">
              {searchTerm 
                ? 'Try adjusting your search terms'
                : 'Start by creating your first job posting'
              }
            </p>
            {!searchTerm && (
              <Link href="/employer/jobs/new" className="btn-primary">
                <i className="fas fa-plus mr-2"></i>
                Post Your First Job
              </Link>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredJobs.map((job) => (
              <div key={job.id} className="card">
                <div className="card-body">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">{job.title}</h3>
                      <p className="text-gray-400 text-sm mb-1">{job.company}</p>
                      <p className="text-gray-500 text-sm mb-2">
                        <i className="fas fa-map-marker-alt mr-1"></i>
                        {job.location}
                      </p>
                      {job.salary && (
                        <p className="text-primary text-sm font-medium mb-2">{job.salary}</p>
                      )}
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      job.status === 'open' 
                        ? 'bg-success/20 text-success'
                        : job.status === 'draft'
                        ? 'bg-warning/20 text-warning'
                        : 'bg-danger/20 text-danger'
                    }`}>
                      {job.status}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>
                      <i className="fas fa-users mr-1"></i>
                      {job.applications_count || 0} applications
                    </span>
                    <span>
                      <i className="fas fa-calendar mr-1"></i>
                      {new Date(job.deadline).toLocaleDateString()}
                    </span>
                  </div>

                  <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                    {job.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      <Link
                        href={`/jobs/${job.id}`}
                        className="btn-outline-primary text-xs"
                      >
                        <i className="fas fa-eye mr-1"></i>
                        View
                      </Link>
                      <Link
                        href={`/employer/jobs/${job.id}/edit`}
                        className="btn-outline-secondary text-xs"
                      >
                        <i className="fas fa-edit mr-1"></i>
                        Edit
                      </Link>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        href={`/employer/jobs/${job.id}/applications`}
                        className="btn-primary text-xs"
                      >
                        <i className="fas fa-file-alt mr-1"></i>
                        Applications
                      </Link>
                      <button
                        onClick={() => handleDeleteJob(job.id)}
                        className="btn-danger text-xs"
                      >
                        <i className="fas fa-trash mr-1"></i>
                        Delete
                      </button>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-700 text-xs text-gray-500">
                    Posted {new Date(job.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
